"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { loginSchema } from "@/util/schema";
import { signIn } from "next-auth/react";

interface LoginImmProps {
  setLoginError: (msg: string | null) => void;
}

const LoginImm: React.FC<LoginImmProps> = ({ setLoginError }) => {
  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: z.infer<typeof loginSchema>) => {
    // Prevent redirect by setting redirect:false
    const result = await signIn("credentials", {
      email: data.email,
      password: data.password,
      redirect: false,
      callbackUrl: window.location.href,
    });

    if (result?.error) {
      setLoginError("Invalid credentials. Please try again.");
    } else {
      setLoginError(null);
      // Redirect on success
      if (result?.url) {
        window.location.href = result.url;
      }
    }
  };

  const otherLogin = async () => {
    await signIn("google", {
      callbackUrl: window.location.href,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
        {/* No local error display here; handled in parent */}
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="grid gap-2">
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                  autoComplete="current-email"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="grid gap-2">
              <div className="flex items-center">
                <FormLabel>Password</FormLabel>
                <a
                  href="/auth/forgot-password"
                  className="ml-auto inline-block text-sm underline"
                >
                  Forgot your password?
                </a>
              </div>
              <FormControl>
                <Input
                  type="password"
                  {...field}
                  autoComplete="current-password"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="w-full text-base bg-gorgonzolaBlue hover:bg-gorgonzolaBlue/70 py-6"
        >
          Login
        </Button>
        <Button
          onClick={() => otherLogin()}
          type="button"
          variant="outline"
          className="w-full py-6 text-base"
        >
          Login with Google
        </Button>
      </form>
    </Form>
  );
};

export default LoginImm;
