"use client";

import React from "react";
import { 
  DollarSign, 
  FileText, 
  CheckCircle, 
  Clock, 
  XCircle,
  TrendingUp,
  Package as PackageIcon,
  Briefcase,
  GraduationCap
} from "lucide-react";
// Using global IProfile interface

interface DashboardBoxProps {
  title: string;
  value: number | string;
  description?: string;
  bgColor?: string;
  icon?: React.ReactNode;
}

const DashboardBox: React.FC<DashboardBoxProps> = ({
  title,
  value,
  description,
  bgColor = "bg-white",
  icon,
}) => (
  <div className={`flex items-center space-x-4 p-6 rounded-lg shadow-md ${bgColor}`}>
    {icon && (
      <div className="flex-shrink-0">
        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          {icon}
        </div>
      </div>
    )}
    <div className="flex-1">
      <h3 className="text-gray-500 font-semibold text-sm">{title}</h3>
      <p className="text-3xl font-bold text-gray-900 mt-1">{value}</p>
      {description && (
        <p className="text-gray-400 text-sm mt-1">{description}</p>
      )}
    </div>
  </div>
);

interface ProfileDashboardProps {
  user: IProfile;
}

const ProfileDashboard: React.FC<ProfileDashboardProps> = ({ user }) => {
  // Calculate case statistics from immigration services
  const immigrationCases = user.immigration_services || [];
  const openCases = immigrationCases.filter(service =>
    service.status === "Open" ||
    service.status === "In Progress" ||
    service.status === "active" ||
    service.status === "in_progress"
  ).length;
  const pendingCases = immigrationCases.filter(service =>
    service.status === "Pending" ||
    service.status === "pending"
  ).length;
  const closedCases = immigrationCases.filter(service =>
    service.status === "Completed" ||
    service.status === "Closed" ||
    service.status === "completed" ||
    service.status === "closed"
  ).length;
  const totalCases = immigrationCases.length;

  // Calculate service statistics
  const totalServices = user.services?.length || 0;
  const totalPackages = user.packages?.length || 0;
  const totalTraining = user.training?.length || 0;

  // Parse total spending (remove currency symbol and convert to number for display)
  const totalSpent = user.total_spent || "$0";
  const spentAmount = totalSpent.replace(/[^0-9.-]+/g, "");
  const spentNumber = parseFloat(spentAmount) || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Welcome back, {user.name}! Here's an overview of your account.</p>
      </div>

      {/* Main Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardBox
          title="Total Spending"
          value={totalSpent}
          description="Total amount spent"
          bgColor="bg-green-50"
          icon={<DollarSign className="w-6 h-6 text-green-600" />}
        />
        <DashboardBox
          title="Immigration Cases"
          value={totalCases}
          description="Total immigration applications"
          bgColor="bg-blue-50"
          icon={<FileText className="w-6 h-6 text-blue-600" />}
        />
        <DashboardBox
          title="Active Services"
          value={totalServices}
          description="Currently enrolled services"
          bgColor="bg-purple-50"
          icon={<Briefcase className="w-6 h-6 text-purple-600" />}
        />
      </div>

      {/* Case Status Breakdown */}
      {totalCases > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Immigration Case Status</h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
              <Clock className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-2xl font-bold text-blue-600">{openCases}</p>
                <p className="text-sm text-gray-600">Open Cases</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 bg-yellow-50 rounded-lg">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold text-yellow-600">{pendingCases}</p>
                <p className="text-sm text-gray-600">Pending Cases</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-green-600">{closedCases}</p>
                <p className="text-sm text-gray-600">Completed Cases</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Services Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Services Overview</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <PackageIcon className="w-5 h-5 text-blue-600" />
                <span className="font-medium">Packages</span>
              </div>
              <span className="text-lg font-bold text-gray-900">{totalPackages}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <GraduationCap className="w-5 h-5 text-green-600" />
                <span className="font-medium">Training Programs</span>
              </div>
              <span className="text-lg font-bold text-gray-900">{totalTraining}</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Briefcase className="w-5 h-5 text-purple-600" />
                <span className="font-medium">Individual Services</span>
              </div>
              <span className="text-lg font-bold text-gray-900">{totalServices}</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Account Summary</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Member Since</span>
              <span className="font-medium">
                {new Date(user.createdAt).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long' 
                })}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Email Verified</span>
              <span className={`font-medium ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                {user.emailVerified ? 'Yes' : 'No'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Total Reviews</span>
              <span className="font-medium">{user.reviews?.length || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Account Status</span>
              <span className="font-medium text-green-600">Active</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity Placeholder */}
      {totalCases === 0 && totalServices === 0 && totalPackages === 0 && (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Started</h3>
          <p className="text-gray-600 mb-4">
            You haven't enrolled in any services yet. Explore our packages and services to get started with your career journey.
          </p>
        </div>
      )}
    </div>
  );
};

export default ProfileDashboard;
