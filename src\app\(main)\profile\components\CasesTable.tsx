// components/CasesTable.tsx
"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Eye } from "lucide-react";

interface Case {
  id: string;
  caseType: string;
  userName: string;
  caseStatus: string;
  priority: string;
  startDate: string;
  endDate: string;
}

interface CasesTableProps {
  cases: Case[];
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

const getStatusBadgeClass = (status: string) => {
  switch (status.toLowerCase()) {
    case "open":
      return "bg-blue-100 text-blue-800";
    case "closed":
      return "bg-green-100 text-green-800";
    case "pending":
      return "bg-yellow-100 text-yellow-800";
    case "in progress":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const CasesTable: React.FC<CasesTableProps> = ({
  cases,
  currentPage,
  itemsPerPage,
  onPageChange,
}) => {
  const router = useRouter();
  const totalPages = Math.ceil(cases.length / itemsPerPage);

  const handleViewDetails = (caseId: string) => {
    router.push(`/profile/application/${caseId}`);
  };

  const paginatedCases = cases.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePrevPage = () => {
    onPageChange(Math.max(currentPage - 1, 1));
  };

  const handleNextPage = () => {
    onPageChange(Math.min(currentPage + 1, totalPages));
  };

  return (
    <div className="overflow-x-auto bg-white rounded-md shadow">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Sno.
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Application ID
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Package
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Name
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Status
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Start Date
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              End Date
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {paginatedCases.map((c, idx) => (
            <tr key={c.id} className="hover:bg-gray-50">
              <td className="px-4 py-2 whitespace-nowrap">
                {(currentPage - 1) * itemsPerPage + idx + 1}
              </td>
              <td className="px-4 py-2 whitespace-nowrap">{c.id}</td>
              <td className="px-4 py-2 whitespace-nowrap">{c.caseType}</td>
              <td className="px-4 py-2 whitespace-nowrap">{c.userName}</td>
              <td className="px-4 py-2 whitespace-nowrap">
                <span
                  className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(
                    c.caseStatus
                  )}`}
                >
                  {c.caseStatus}
                </span>
              </td>
              <td className="px-4 py-2 whitespace-nowrap">{c.startDate}</td>
              <td className="px-4 py-2 whitespace-nowrap">{c.endDate}</td>
              <td className="px-4 py-2 whitespace-nowrap">
                <Button
                  onClick={() => handleViewDetails(c.id)}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                >
                  <Eye size={16} />
                  View
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Pagination Controls */}
      <div className="flex justify-between items-center px-4 py-2 bg-gray-100 border-t border-gray-200 rounded-b-md">
        <button
          onClick={handlePrevPage}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded-md ${
            currentPage === 1
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-600 text-white hover:bg-blue-700"
          }`}
        >
          Previous
        </button>
        <span className="text-sm text-gray-700">
          Page {currentPage} of {totalPages}
        </span>
        <button
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded-md ${
            currentPage === totalPages
              ? "bg-gray-300 cursor-not-allowed"
              : "bg-blue-600 text-white hover:bg-blue-700"
          }`}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default CasesTable;
