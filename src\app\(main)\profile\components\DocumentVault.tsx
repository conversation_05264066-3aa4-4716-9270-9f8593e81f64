"use client";

import { SendHorizonalIcon } from "lucide-react";
import React, { useState, useCallback } from "react";

type DocumentType = "Passport" | "Photo" | "Education Proof" | "Address Proof";

type DocumentStatus = "Pending" | "Approved" | "Failed";

interface UploadedDoc {
  id: string;
  type: DocumentType;
  name: string;
  status: DocumentStatus;
  uploadedAt: Date;
}

const DOCUMENT_TYPES: DocumentType[] = [
  "Passport",
  "Photo",
  "Education Proof",
  "Address Proof",
];

// Static example documents with all 3 statuses
const staticUploadedDocs: UploadedDoc[] = [
  {
    id: "s1",
    type: "Passport",
    name: "passport_scan.pdf",
    status: "Approved",
    uploadedAt: new Date("2025-05-01T10:30:00"),
  },
  {
    id: "s2",
    type: "Photo",
    name: "profile_photo.jpg",
    status: "Pending",
    uploadedAt: new Date("2025-05-02T15:45:00"),
  },
  {
    id: "s3",
    type: "Education Proof",
    name: "degree_certificate.pdf",
    status: "Failed",
    uploadedAt: new Date("2025-04-28T08:20:00"),
  },
  {
    id: "s4",
    type: "Address Proof",
    name: "utility_bill.jpg",
    status: "Pending",
    uploadedAt: new Date("2025-05-03T12:00:00"),
  },
];

const DocumentVault: React.FC = () => {
  const [uploadedDocs, setUploadedDocs] = useState<UploadedDoc[]>([]);

  const onDrop = useCallback(
    (type: DocumentType, files: FileList | null) => {
      if (!files) return;
      const newDocs = Array.from(files).map((file) => ({
        id: `${type}-${file.name}-${Date.now()}`,
        type,
        name: file.name,
        status: "Pending" as DocumentStatus,
        uploadedAt: new Date(),
      }));
      setUploadedDocs((prev) => [...prev, ...newDocs]);
    },
    []
  );

  const createDragEvents = (type: DocumentType) => {
    const onDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
    };
    const onDropHandler = (e: React.DragEvent) => {
      e.preventDefault();
      onDrop(type, e.dataTransfer.files);
    };
    return { onDragOver, onDrop: onDropHandler };
  };

  const onFileInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: DocumentType
  ) => {
    onDrop(type, e.target.files);
  };

  const allDocs = [...staticUploadedDocs, ...uploadedDocs];

  // Map status to badge styles
  const getStatusBadgeClass = (status: DocumentStatus) => {
    switch (status) {
      case "Approved":
        return "bg-green-100 text-green-800";
      case "Pending":
        return "bg-yellow-100 text-yellow-800";
      case "Failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-4 space-y-8 max-w-4xl mx-auto">
      {/* <h2 className="text-2xl font-semibold mb-4">Document Vault</h2> */}

      {/* <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {DOCUMENT_TYPES.map((type) => {
          const { onDragOver, onDrop } = createDragEvents(type);
          return (
            <div
              key={type}
              onDragOver={onDragOver}
              onDrop={onDrop}
              className="border-2 border-dashed border-gray-400 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
            >
              <label
                htmlFor={`upload-${type}`}
                className="block cursor-pointer text-lg font-medium text-gray-700 mb-3"
              >
                Drag & Drop or Click to Upload <br />
                <span className="font-bold">{type}</span>
              </label>
              <input
                id={`upload-${type}`}
                type="file"
                multiple
                className="hidden"
                onChange={(e) => onFileInputChange(e, type)}
                accept="image/*,.pdf"
              />
            </div>
          );
        })}
      </div> */}

      <div>
        <h3 className="text-xl font-semibold mb-2">Uploaded Documents</h3>
        {allDocs.length === 0 ? (
          <p className="text-gray-500">No documents uploaded yet.</p>
        ) : (
          <div className="overflow-x-auto border rounded-md shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-100">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                    Document
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                    File Name
                  </th>
                  {/* <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                    Status
                  </th> */}
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                    Uploaded At
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {allDocs.map(({ id, type, name, uploadedAt }) => (
                  <tr key={id} className="hover:bg-gray-50">
                    <td className="px-4 py-2 whitespace-nowrap">{type}</td>
                    <td className="px-4 py-2 whitespace-nowrap">{name}</td>
                    {/* <td className="px-4 py-2 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(
                          status
                        )}`}
                      >
                        {status}
                      </span>
                    </td> */}
                    <td className="px-4 py-2 whitespace-nowrap">
                      {uploadedAt.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentVault;
